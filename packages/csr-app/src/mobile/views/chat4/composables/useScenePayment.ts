/**
 * 场景付费管理 Composable
 * 专门用于chat4的场景跳转付费功能
 */
import { ref, computed } from 'vue'
import { useStoryStore } from '@/store/story'
import { useUserStore } from '@/store/user'
import { useChatEventsStore } from '@/store/chat-events'
import { getSceneOptions, purchaseSceneOption } from '@/api/scene-options'
import type { SceneOption } from '@/api/scene-options'
import { Message } from '@/mobile/components/Message'

// 场景付费状态管理
const scenePaymentData = ref<SceneOption[]>([])
const isLoading = ref(false)

export function useScenePayment() {
  const storyStore = useStoryStore()
  const userStore = useUserStore()
  const chatEventsStore = useChatEventsStore()

  // 获取场景付费选项
  const fetchSceneOptions = async (): Promise<SceneOption[]> => {
    try {
      isLoading.value = true
      const storyId = storyStore.currentStory?.id
      const actorId = storyStore.currentActor?.id

      if (!storyId || !actorId) {
        console.warn('无法获取场景付费选项：缺少story或actor信息')
        return []
      }

      const response = await getSceneOptions(storyId, { actor_id: actorId })
      if (response.code === '0' && response.data) {
        const sceneIds = response.data.options || []
        // 过滤掉需要好感度解锁或金币解锁的场景，这些场景不参与付费逻辑
        const filteredSceneIds = sceneIds.filter(
          (sceneId: string) =>
            !isHeartValueScene(sceneId) && !isCoinsScene(sceneId),
        )

        // 转换为内部格式
        const options: SceneOption[] = filteredSceneIds.map(
          (sceneId: string) => ({
            option_id: sceneId,
            scene_id: sceneId,
            is_purchased: true, // 如果在列表中，说明已经访问过
            coins_required: getSceneCoinsRequired(sceneId),
            is_first_access: false,
          }),
        )

        scenePaymentData.value = options

        console.log('成功获取场景付费选项:', {
          raw: sceneIds,
          processed: options,
        })
        return options
      } else {
        console.error('获取场景付费选项失败:', response.message)
        return []
      }
    } catch (error) {
      console.error('获取场景付费选项异常:', error)
      return []
    } finally {
      isLoading.value = false
    }
  }

  // 检查场景是否需要付费
  const isSceneRequiresPayment = async (sceneId: string): Promise<boolean> => {
    // 只有chat4才需要付费检查
    const isChat4 =
      storyStore.currentActor?.version === '4' ||
      storyStore.currentActor?.version === '5' ||
      storyStore.currentStory?.version === '4'

    if (!isChat4) {
      return false
    }

    // 好感度解锁或金币解锁场景不受付费逻辑影响
    if (isHeartValueScene(sceneId) || isCoinsScene(sceneId)) {
      return false
    }

    const options = await fetchSceneOptions()

    // 共享第一次免费机制：只要有任何付费记录，其他场景都要付费
    if (options.length > 0) {
      return true
    }

    // 如果没有任何付费记录，说明是第一次访问，不需要付费
    return false
  }

  // 获取场景所需的硬币数量（直接从好感度配置获取）
  const getSceneCoinsRequired = (sceneId: string): number => {
    const favorabilityState = chatEventsStore.favorabilityState.value

    // 安全检查：确保favorabilityState存在且有sceneConditions
    if (!favorabilityState || !favorabilityState.sceneConditions) {
      console.warn(
        'favorabilityState or sceneConditions not available, returning 0 coins required',
      )
      return 0
    }

    const { sceneConditions } = favorabilityState

    // 查找对应场景的好感度要求
    const sceneCondition = sceneConditions.find(
      (condition: any) => condition.scene_id === sceneId,
    )

    if (sceneCondition && sceneCondition.requirement === 'heart_value') {
      return sceneCondition.value // 使用好感度值作为硬币数量
    }

    // 如果是金币要求，也返回相应数量
    if (sceneCondition && sceneCondition.requirement === 'coins') {
      return sceneCondition.value
    }

    // 默认值
    return 0
  }

  // 购买场景访问权限（包括第一次免费记录和付费购买）
  const purchaseSceneAccess = async (
    sceneId: string,
    isFirstAccess = false,
  ): Promise<boolean> => {
    try {
      isLoading.value = true
      const storyId = storyStore.currentStory?.id
      const actorId = storyStore.currentActor?.id

      if (!storyId || !actorId) {
        Message.error('操作失败：缺少必要信息')
        return false
      }

      // 如果不是第一次访问，需要检查硬币是否足够
      if (!isFirstAccess) {
        const coinsRequired = getSceneCoinsRequired(sceneId)
        const userCoins = userStore.userInfo?.coins || 0
        if (userCoins < coinsRequired) {
          Message.error(`硬币不足，需要 ${coinsRequired} 硬币`)
          return false
        }
      }

      // 使用场景ID作为option_id
      const optionId = sceneId

      // 执行购买/记录
      const response = await purchaseSceneOption(storyId, {
        actor_id: actorId,
        option_id: optionId,
      })

      if (response.code === '0') {
        // 更新用户硬币数量（如果有扣费）
        if (
          userStore.userInfo &&
          response.data?.remaining_coins !== undefined
        ) {
          userStore.userInfo.coins = response.data.remaining_coins
        }

        // 清除数据，下次重新获取
        scenePaymentData.value = []

        if (isFirstAccess) {
          console.log(`首次访问场景 ${sceneId} 已记录`)
        } else {
          console.log(`场景 ${sceneId} 付费购买成功`)
        }
        return true
      } else {
        console.log(response, '222222')
        Message.error(response.message || '操作失败')
        return false
      }
    } catch (error) {
      console.error('购买场景访问权限异常:', error)
      Message.error('操作失败，请稍后重试')
      return false
    } finally {
      isLoading.value = false
    }
  }

  // 清除数据
  const clearData = () => {
    scenePaymentData.value = []
  }

  // 计算属性：当前是否为chat4
  const isChat4 = computed(() => {
    return (
      storyStore.currentActor?.version === '4' ||
      storyStore.currentActor?.version === '5' ||
      storyStore.currentStory?.version === '4'
    )
  })

  // 所有可能的场景常量
  const ALL_SCENES = [
    'Video',
    'Monitor',
    'Meetup',
    'Dancing',
    'Concert',
    'Tip',
    'Moment',
    'Living', // 添加 Living 场景
  ] as const

  // 动态判断场景是否需要好感度解锁（不受付费逻辑影响）
  const isHeartValueScene = (sceneId: string): boolean => {
    const favorabilityState = chatEventsStore.favorabilityState.value
    if (!favorabilityState || !favorabilityState.sceneConditions) {
      return false
    }

    const condition = favorabilityState.sceneConditions.find(
      (c: any) => c.scene_id === sceneId,
    )
    return condition?.requirement === 'heart_value'
  }

  // 动态判断场景是否需要金币解锁（直接金币解锁，不参与付费逻辑）
  const isCoinsScene = (sceneId: string): boolean => {
    const favorabilityState = chatEventsStore.favorabilityState.value
    if (!favorabilityState || !favorabilityState.sceneConditions) {
      return false
    }

    const condition = favorabilityState.sceneConditions.find(
      (c: any) => c.scene_id === sceneId,
    )
    return condition?.requirement === 'coins'
  }

  // 获取按钮的付费状态（用于UI显示）
  const getSceneButtonState = computed(() => {
    const isChat4Current =
      storyStore.currentActor?.version === '4' ||
      storyStore.currentActor?.version === '5' ||
      storyStore.currentStory?.version === '4'

    if (!isChat4Current) {
      return {} // 非chat4返回空对象
    }

    // 检查是否已经有任何试用记录（共享第一次免费机制）
    const hasAnyPaymentRecord = scenePaymentData.value.length > 0

    return Object.fromEntries(
      ALL_SCENES.map((sceneId) => [
        sceneId,
        // 好感度解锁场景或金币解锁场景不受付费逻辑影响
        isHeartValueScene(sceneId) || isCoinsScene(sceneId)
          ? {
              requiresPayment: false, // 由解锁系统控制，这里不显示付费状态
            }
          : hasAnyPaymentRecord
          ? {
              requiresPayment: true,
              coinsRequired: getSceneCoinsRequired(sceneId),
            }
          : {
              requiresPayment: false, // 第一次免费
            },
      ]),
    )
  })

  // 检查是否有任何试用记录
  const hasAnyTrialRecord = (): boolean => {
    return scenePaymentData.value.length > 0
  }

  return {
    isLoading: computed(() => isLoading.value),
    isChat4,
    fetchSceneOptions,
    isSceneRequiresPayment,
    getSceneCoinsRequired,
    purchaseSceneAccess,
    clearData,
    sceneButtonStates: getSceneButtonState, // 新增：按钮状态
    hasAnyTrialRecord, // 新增：试用记录检查
  }
}
